{"version": 3, "names": [], "sources": ["../src/index.ts"], "sourcesContent": ["/**\n * Android platform files\n */\n\nexport {default as commands} from './commands';\nexport {\n  adb,\n  getAdbPath,\n  listAndroidDevices,\n  tryRunAdbReverse,\n} from './commands/runAndroid';\nexport {\n  projectConfig,\n  dependencyConfig,\n  getAndroidProject,\n  getPackageName,\n  isProjectUsingKotlin,\n} from '@react-native-community/cli-config-android';\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIA;AACA;AAMA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAMoD"}