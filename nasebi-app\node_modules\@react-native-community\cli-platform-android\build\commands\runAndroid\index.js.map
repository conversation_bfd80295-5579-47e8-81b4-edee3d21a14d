{"version": 3, "names": ["runAndroid", "_argv", "config", "args", "link", "setPlatform", "packager", "port", "newPort", "startPackager", "findDevServerPort", "root", "startServerInNewWindow", "reactNativePath", "terminal", "reactNativeVersion", "setVersion", "binaryPath", "tasks", "CLIError", "path", "isAbsolute", "join", "fs", "existsSync", "androidProject", "getAndroidProject", "mainActivity", "buildAndRun", "defaultPort", "getAvailableDevicePort", "adbPath", "getAdbPath", "devices", "adb", "getDevices", "some", "d", "includes", "toString", "deviceId", "logger", "warn", "device", "process", "chdir", "sourceDir", "cmd", "platform", "startsWith", "selectedTask", "interactive", "task", "promptForTaskSelection", "listDevices", "listAndroidDevices", "users", "checkUsers", "length", "user", "promptForUser", "id", "connected", "runOnSpecificDevice", "emulator", "info", "result", "tryLaunchEmulator", "readableName", "success", "chalk", "dim", "error", "runOnAllDevices", "buildTask", "replace", "indexOf", "grad<PERSON><PERSON><PERSON><PERSON>", "getTaskNames", "appName", "mode", "push", "extraParams", "activeArchOnly", "architecture", "getCPU", "build", "installAndLaunchOnDevice", "selected<PERSON><PERSON><PERSON>", "tryRunAdbReverse", "tryInstallAppOnDevice", "tryLaunchAppOnDevice", "name", "description", "func", "options", "default", "env", "RCT_METRO_PORT", "parse", "Number", "getDefaultUserTerminal"], "sources": ["../../../src/commands/runAndroid/index.ts"], "sourcesContent": ["/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\nimport fs from 'fs';\nimport {Config} from '@react-native-community/cli-types';\nimport adb from './adb';\nimport runOnAllDevices from './runOnAllDevices';\nimport tryRunAdbReverse from './tryRunAdbReverse';\nimport tryLaunchAppOnDevice from './tryLaunchAppOnDevice';\nimport tryInstallAppOnDevice from './tryInstallAppOnDevice';\nimport getAdbPath from './getAdbPath';\nimport {\n  logger,\n  CLIError,\n  link,\n  getDefaultUserTerminal,\n  startServerInNewWindow,\n  findDevServerPort,\n} from '@react-native-community/cli-tools';\nimport {getAndroidProject} from '@react-native-community/cli-config-android';\nimport listAndroidDevices from './listAndroidDevices';\nimport tryLaunchEmulator from './tryLaunchEmulator';\nimport chalk from 'chalk';\nimport path from 'path';\nimport {build, BuildFlags, options} from '../buildAndroid';\nimport {promptForTaskSelection} from './listAndroidTasks';\nimport {getTaskNames} from './getTaskNames';\nimport {checkUsers, promptForUser} from './listAndroidUsers';\n\nexport interface Flags extends BuildFlags {\n  appId: string;\n  appIdSuffix: string;\n  mainActivity?: string;\n  port: number;\n  terminal?: string;\n  packager?: boolean;\n  device?: string;\n  deviceId?: string;\n  listDevices?: boolean;\n  binaryPath?: string;\n  user?: number | string;\n}\n\nexport type AndroidProject = NonNullable<Config['project']['android']>;\n\n/**\n * Starts the app on a connected Android emulator or device.\n */\nasync function runAndroid(_argv: Array<string>, config: Config, args: Flags) {\n  link.setPlatform('android');\n\n  let {packager, port} = args;\n\n  if (packager) {\n    const {port: newPort, startPackager} = await findDevServerPort(\n      port,\n      config.root,\n    );\n\n    if (startPackager) {\n      // Awaiting this causes the CLI to hang indefinitely, so this must execute without await.\n      startServerInNewWindow(\n        newPort,\n        config.root,\n        config.reactNativePath,\n        args.terminal,\n      );\n    }\n  }\n\n  if (config.reactNativeVersion !== 'unknown') {\n    link.setVersion(config.reactNativeVersion);\n  }\n\n  if (args.binaryPath) {\n    if (args.tasks) {\n      throw new CLIError(\n        'binary-path and tasks were specified, but they are not compatible. Specify only one',\n      );\n    }\n\n    args.binaryPath = path.isAbsolute(args.binaryPath)\n      ? args.binaryPath\n      : path.join(config.root, args.binaryPath);\n\n    if (args.binaryPath && !fs.existsSync(args.binaryPath)) {\n      throw new CLIError(\n        'binary-path was specified, but the file was not found.',\n      );\n    }\n  }\n\n  let androidProject = getAndroidProject(config);\n\n  if (args.mainActivity) {\n    androidProject.mainActivity = args.mainActivity;\n  }\n\n  return buildAndRun(args, androidProject);\n}\n\nconst defaultPort = 5552;\nasync function getAvailableDevicePort(\n  port: number = defaultPort,\n): Promise<number> {\n  /**\n   * The default value is 5554 for the first virtual device instance running on your machine. A virtual device normally occupies a pair of adjacent ports: a console port and an adb port. The console of the first virtual device running on a particular machine uses console port 5554 and adb port 5555. Subsequent instances use port numbers increasing by two. For example, 5556/5557, 5558/5559, and so on. The range is 5554 to 5682, allowing for 64 concurrent virtual devices.\n   */\n  const adbPath = getAdbPath();\n  const devices = adb.getDevices(adbPath);\n  if (port > 5682) {\n    throw new CLIError('Failed to launch emulator...');\n  }\n  if (devices.some((d) => d.includes(port.toString()))) {\n    return await getAvailableDevicePort(port + 2);\n  }\n  return port;\n}\n\n// Builds the app and runs it on a connected emulator / device.\nasync function buildAndRun(args: Flags, androidProject: AndroidProject) {\n  if (args.deviceId) {\n    logger.warn(\n      'The `deviceId` parameter is renamed to `device`. Please use the new `device` argument next time to avoid this warning.',\n    );\n    args.device = args.deviceId;\n  }\n\n  process.chdir(androidProject.sourceDir);\n  const cmd = process.platform.startsWith('win') ? 'gradlew.bat' : './gradlew';\n\n  const adbPath = getAdbPath();\n\n  let selectedTask;\n\n  if (args.interactive) {\n    const task = await promptForTaskSelection(\n      'install',\n      androidProject.sourceDir,\n    );\n    if (task) {\n      selectedTask = task;\n    }\n  }\n\n  if (args.listDevices || args.interactive) {\n    if (args.device) {\n      logger.warn(\n        `Both ${\n          args.deviceId ? 'deviceId' : 'device'\n        } and \"list-devices\" parameters were passed to \"run\" command. We will list available devices and let you choose from one`,\n      );\n    }\n\n    const device = await listAndroidDevices();\n    if (!device) {\n      throw new CLIError(\n        `Failed to select device, please try to run app without ${\n          args.listDevices ? 'list-devices' : 'interactive'\n        } command.`,\n      );\n    }\n\n    if (args.interactive) {\n      const users = checkUsers(device.deviceId as string, adbPath);\n      if (users && users.length > 1) {\n        const user = await promptForUser(users);\n\n        if (user) {\n          args.user = user.id;\n        }\n      }\n    }\n\n    if (device.connected) {\n      return runOnSpecificDevice(\n        {...args, device: device.deviceId},\n        adbPath,\n        androidProject,\n        selectedTask,\n      );\n    }\n\n    const port = await getAvailableDevicePort();\n    const emulator = `emulator-${port}`;\n    logger.info('Launching emulator...');\n    const result = await tryLaunchEmulator(adbPath, device.readableName, port);\n    if (result.success) {\n      logger.info('Successfully launched emulator.');\n      return runOnSpecificDevice(\n        {...args, device: emulator},\n        adbPath,\n        androidProject,\n        selectedTask,\n      );\n    }\n    throw new CLIError(\n      `Failed to launch emulator. Reason: ${chalk.dim(result.error || '')}`,\n    );\n  }\n\n  if (args.device) {\n    return runOnSpecificDevice(args, adbPath, androidProject, selectedTask);\n  } else {\n    return runOnAllDevices(args, cmd, adbPath, androidProject);\n  }\n}\n\nfunction runOnSpecificDevice(\n  args: Flags,\n  adbPath: string,\n  androidProject: AndroidProject,\n  selectedTask?: string,\n) {\n  const devices = adb.getDevices(adbPath);\n  const {device} = args;\n\n  // if coming from run-android command and we have selected task\n  // from interactive mode we need to create appropriate build task\n  // eg 'installRelease' -> 'assembleRelease'\n  const buildTask = selectedTask\n    ? [selectedTask.replace('install', 'assemble')]\n    : [];\n\n  if (devices.length > 0 && device) {\n    if (devices.indexOf(device) !== -1) {\n      let gradleArgs = getTaskNames(\n        androidProject.appName,\n        args.mode,\n        args.tasks ?? buildTask,\n        'install',\n      );\n\n      // using '-x lint' in order to ignore linting errors while building the apk\n      gradleArgs.push('-x', 'lint');\n      if (args.extraParams) {\n        gradleArgs.push(...args.extraParams);\n      }\n\n      if (args.port) {\n        gradleArgs.push(`-PreactNativeDevServerPort=${args.port}`);\n      }\n\n      if (args.activeArchOnly) {\n        const architecture = adb.getCPU(adbPath, device);\n\n        if (architecture !== null) {\n          logger.info(`Detected architecture ${architecture}`);\n          // `reactNativeDebugArchitectures` was renamed to `reactNativeArchitectures` in 0.68.\n          // Can be removed when 0.67 no longer needs to be supported.\n          gradleArgs.push(`-PreactNativeDebugArchitectures=${architecture}`);\n          gradleArgs.push(`-PreactNativeArchitectures=${architecture}`);\n        }\n      }\n\n      if (!args.binaryPath) {\n        build(gradleArgs, androidProject.sourceDir);\n      }\n\n      installAndLaunchOnDevice(\n        args,\n        device,\n        adbPath,\n        androidProject,\n        selectedTask,\n      );\n    } else {\n      logger.error(\n        `Could not find device: \"${device}\". Please choose one of the following:`,\n        ...devices,\n      );\n    }\n  } else {\n    logger.error('No Android device or emulator connected.');\n  }\n}\n\nfunction installAndLaunchOnDevice(\n  args: Flags,\n  selectedDevice: string,\n  adbPath: string,\n  androidProject: AndroidProject,\n  selectedTask?: string,\n) {\n  tryRunAdbReverse(args.port, selectedDevice);\n\n  tryInstallAppOnDevice(\n    args,\n    adbPath,\n    selectedDevice,\n    androidProject,\n    selectedTask,\n  );\n\n  tryLaunchAppOnDevice(selectedDevice, androidProject, adbPath, args);\n}\n\nexport default {\n  name: 'run-android',\n  description:\n    'builds your app and starts it on a connected Android emulator or device',\n  func: runAndroid,\n  options: [\n    ...options,\n    {\n      name: '--no-packager',\n      description: 'Do not launch packager while running the app',\n    },\n    {\n      name: '--port <number>',\n      default: process.env.RCT_METRO_PORT || 8081,\n      parse: Number,\n    },\n    {\n      name: '--terminal <string>',\n      description:\n        'Launches the Metro Bundler in a new window using the specified terminal path.',\n      default: getDefaultUserTerminal(),\n    },\n    {\n      name: '--appId <string>',\n      description:\n        'Specify an applicationId to launch after build. If not specified, `package` from AndroidManifest.xml will be used.',\n      default: '',\n    },\n    {\n      name: '--appIdSuffix <string>',\n      description: 'Specify an applicationIdSuffix to launch after build.',\n      default: '',\n    },\n    {\n      name: '--main-activity <string>',\n      description: 'Name of the activity to start',\n    },\n    {\n      name: '--device <string>',\n      description:\n        'Explicitly set the device to use by name. The value is not required ' +\n        'if you have a single device connected.',\n    },\n    {\n      name: '--deviceId <string>',\n      description:\n        '**DEPRECATED** Builds your app and starts it on a specific device/simulator with the ' +\n        'given device id (listed by running \"adb devices\" on the command line).',\n    },\n    {\n      name: '--list-devices',\n      description:\n        'Lists all available Android devices and simulators and let you choose one to run the app',\n      default: false,\n    },\n    {\n      name: '--binary-path <string>',\n      description:\n        'Path relative to project root where pre-built .apk binary lives.',\n    },\n    {\n      name: '--user <number>',\n      description: 'Id of the User Profile you want to install the app on.',\n      parse: Number,\n    },\n  ],\n};\n\nexport {adb, getAdbPath, listAndroidDevices, tryRunAdbReverse};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAQA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;AACA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;AACA;AACA;AACA;AAA6D;AA/B7D;AACA;AACA;AACA;AACA;AACA;AACA;;AA2CA;AACA;AACA;AACA,eAAeA,UAAU,CAACC,KAAoB,EAAEC,MAAc,EAAEC,IAAW,EAAE;EAC3EC,gBAAI,CAACC,WAAW,CAAC,SAAS,CAAC;EAE3B,IAAI;IAACC,QAAQ;IAAEC;EAAI,CAAC,GAAGJ,IAAI;EAE3B,IAAIG,QAAQ,EAAE;IACZ,MAAM;MAACC,IAAI,EAAEC,OAAO;MAAEC;IAAa,CAAC,GAAG,MAAM,IAAAC,6BAAiB,EAC5DH,IAAI,EACJL,MAAM,CAACS,IAAI,CACZ;IAED,IAAIF,aAAa,EAAE;MACjB;MACA,IAAAG,kCAAsB,EACpBJ,OAAO,EACPN,MAAM,CAACS,IAAI,EACXT,MAAM,CAACW,eAAe,EACtBV,IAAI,CAACW,QAAQ,CACd;IACH;EACF;EAEA,IAAIZ,MAAM,CAACa,kBAAkB,KAAK,SAAS,EAAE;IAC3CX,gBAAI,CAACY,UAAU,CAACd,MAAM,CAACa,kBAAkB,CAAC;EAC5C;EAEA,IAAIZ,IAAI,CAACc,UAAU,EAAE;IACnB,IAAId,IAAI,CAACe,KAAK,EAAE;MACd,MAAM,KAAIC,oBAAQ,EAChB,qFAAqF,CACtF;IACH;IAEAhB,IAAI,CAACc,UAAU,GAAGG,eAAI,CAACC,UAAU,CAAClB,IAAI,CAACc,UAAU,CAAC,GAC9Cd,IAAI,CAACc,UAAU,GACfG,eAAI,CAACE,IAAI,CAACpB,MAAM,CAACS,IAAI,EAAER,IAAI,CAACc,UAAU,CAAC;IAE3C,IAAId,IAAI,CAACc,UAAU,IAAI,CAACM,aAAE,CAACC,UAAU,CAACrB,IAAI,CAACc,UAAU,CAAC,EAAE;MACtD,MAAM,KAAIE,oBAAQ,EAChB,wDAAwD,CACzD;IACH;EACF;EAEA,IAAIM,cAAc,GAAG,IAAAC,qCAAiB,EAACxB,MAAM,CAAC;EAE9C,IAAIC,IAAI,CAACwB,YAAY,EAAE;IACrBF,cAAc,CAACE,YAAY,GAAGxB,IAAI,CAACwB,YAAY;EACjD;EAEA,OAAOC,WAAW,CAACzB,IAAI,EAAEsB,cAAc,CAAC;AAC1C;AAEA,MAAMI,WAAW,GAAG,IAAI;AACxB,eAAeC,sBAAsB,CACnCvB,IAAY,GAAGsB,WAAW,EACT;EACjB;AACF;AACA;EACE,MAAME,OAAO,GAAG,IAAAC,mBAAU,GAAE;EAC5B,MAAMC,OAAO,GAAGC,YAAG,CAACC,UAAU,CAACJ,OAAO,CAAC;EACvC,IAAIxB,IAAI,GAAG,IAAI,EAAE;IACf,MAAM,KAAIY,oBAAQ,EAAC,8BAA8B,CAAC;EACpD;EACA,IAAIc,OAAO,CAACG,IAAI,CAAEC,CAAC,IAAKA,CAAC,CAACC,QAAQ,CAAC/B,IAAI,CAACgC,QAAQ,EAAE,CAAC,CAAC,EAAE;IACpD,OAAO,MAAMT,sBAAsB,CAACvB,IAAI,GAAG,CAAC,CAAC;EAC/C;EACA,OAAOA,IAAI;AACb;;AAEA;AACA,eAAeqB,WAAW,CAACzB,IAAW,EAAEsB,cAA8B,EAAE;EACtE,IAAItB,IAAI,CAACqC,QAAQ,EAAE;IACjBC,kBAAM,CAACC,IAAI,CACT,wHAAwH,CACzH;IACDvC,IAAI,CAACwC,MAAM,GAAGxC,IAAI,CAACqC,QAAQ;EAC7B;EAEAI,OAAO,CAACC,KAAK,CAACpB,cAAc,CAACqB,SAAS,CAAC;EACvC,MAAMC,GAAG,GAAGH,OAAO,CAACI,QAAQ,CAACC,UAAU,CAAC,KAAK,CAAC,GAAG,aAAa,GAAG,WAAW;EAE5E,MAAMlB,OAAO,GAAG,IAAAC,mBAAU,GAAE;EAE5B,IAAIkB,YAAY;EAEhB,IAAI/C,IAAI,CAACgD,WAAW,EAAE;IACpB,MAAMC,IAAI,GAAG,MAAM,IAAAC,wCAAsB,EACvC,SAAS,EACT5B,cAAc,CAACqB,SAAS,CACzB;IACD,IAAIM,IAAI,EAAE;MACRF,YAAY,GAAGE,IAAI;IACrB;EACF;EAEA,IAAIjD,IAAI,CAACmD,WAAW,IAAInD,IAAI,CAACgD,WAAW,EAAE;IACxC,IAAIhD,IAAI,CAACwC,MAAM,EAAE;MACfF,kBAAM,CAACC,IAAI,CACR,QACCvC,IAAI,CAACqC,QAAQ,GAAG,UAAU,GAAG,QAC9B,yHAAwH,CAC1H;IACH;IAEA,MAAMG,MAAM,GAAG,MAAM,IAAAY,2BAAkB,GAAE;IACzC,IAAI,CAACZ,MAAM,EAAE;MACX,MAAM,KAAIxB,oBAAQ,EACf,0DACChB,IAAI,CAACmD,WAAW,GAAG,cAAc,GAAG,aACrC,WAAU,CACZ;IACH;IAEA,IAAInD,IAAI,CAACgD,WAAW,EAAE;MACpB,MAAMK,KAAK,GAAG,IAAAC,4BAAU,EAACd,MAAM,CAACH,QAAQ,EAAYT,OAAO,CAAC;MAC5D,IAAIyB,KAAK,IAAIA,KAAK,CAACE,MAAM,GAAG,CAAC,EAAE;QAC7B,MAAMC,IAAI,GAAG,MAAM,IAAAC,+BAAa,EAACJ,KAAK,CAAC;QAEvC,IAAIG,IAAI,EAAE;UACRxD,IAAI,CAACwD,IAAI,GAAGA,IAAI,CAACE,EAAE;QACrB;MACF;IACF;IAEA,IAAIlB,MAAM,CAACmB,SAAS,EAAE;MACpB,OAAOC,mBAAmB,CACxB;QAAC,GAAG5D,IAAI;QAAEwC,MAAM,EAAEA,MAAM,CAACH;MAAQ,CAAC,EAClCT,OAAO,EACPN,cAAc,EACdyB,YAAY,CACb;IACH;IAEA,MAAM3C,IAAI,GAAG,MAAMuB,sBAAsB,EAAE;IAC3C,MAAMkC,QAAQ,GAAI,YAAWzD,IAAK,EAAC;IACnCkC,kBAAM,CAACwB,IAAI,CAAC,uBAAuB,CAAC;IACpC,MAAMC,MAAM,GAAG,MAAM,IAAAC,0BAAiB,EAACpC,OAAO,EAAEY,MAAM,CAACyB,YAAY,EAAE7D,IAAI,CAAC;IAC1E,IAAI2D,MAAM,CAACG,OAAO,EAAE;MAClB5B,kBAAM,CAACwB,IAAI,CAAC,iCAAiC,CAAC;MAC9C,OAAOF,mBAAmB,CACxB;QAAC,GAAG5D,IAAI;QAAEwC,MAAM,EAAEqB;MAAQ,CAAC,EAC3BjC,OAAO,EACPN,cAAc,EACdyB,YAAY,CACb;IACH;IACA,MAAM,KAAI/B,oBAAQ,EACf,sCAAqCmD,gBAAK,CAACC,GAAG,CAACL,MAAM,CAACM,KAAK,IAAI,EAAE,CAAE,EAAC,CACtE;EACH;EAEA,IAAIrE,IAAI,CAACwC,MAAM,EAAE;IACf,OAAOoB,mBAAmB,CAAC5D,IAAI,EAAE4B,OAAO,EAAEN,cAAc,EAAEyB,YAAY,CAAC;EACzE,CAAC,MAAM;IACL,OAAO,IAAAuB,wBAAe,EAACtE,IAAI,EAAE4C,GAAG,EAAEhB,OAAO,EAAEN,cAAc,CAAC;EAC5D;AACF;AAEA,SAASsC,mBAAmB,CAC1B5D,IAAW,EACX4B,OAAe,EACfN,cAA8B,EAC9ByB,YAAqB,EACrB;EACA,MAAMjB,OAAO,GAAGC,YAAG,CAACC,UAAU,CAACJ,OAAO,CAAC;EACvC,MAAM;IAACY;EAAM,CAAC,GAAGxC,IAAI;;EAErB;EACA;EACA;EACA,MAAMuE,SAAS,GAAGxB,YAAY,GAC1B,CAACA,YAAY,CAACyB,OAAO,CAAC,SAAS,EAAE,UAAU,CAAC,CAAC,GAC7C,EAAE;EAEN,IAAI1C,OAAO,CAACyB,MAAM,GAAG,CAAC,IAAIf,MAAM,EAAE;IAChC,IAAIV,OAAO,CAAC2C,OAAO,CAACjC,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE;MAClC,IAAIkC,UAAU,GAAG,IAAAC,0BAAY,EAC3BrD,cAAc,CAACsD,OAAO,EACtB5E,IAAI,CAAC6E,IAAI,EACT7E,IAAI,CAACe,KAAK,IAAIwD,SAAS,EACvB,SAAS,CACV;;MAED;MACAG,UAAU,CAACI,IAAI,CAAC,IAAI,EAAE,MAAM,CAAC;MAC7B,IAAI9E,IAAI,CAAC+E,WAAW,EAAE;QACpBL,UAAU,CAACI,IAAI,CAAC,GAAG9E,IAAI,CAAC+E,WAAW,CAAC;MACtC;MAEA,IAAI/E,IAAI,CAACI,IAAI,EAAE;QACbsE,UAAU,CAACI,IAAI,CAAE,8BAA6B9E,IAAI,CAACI,IAAK,EAAC,CAAC;MAC5D;MAEA,IAAIJ,IAAI,CAACgF,cAAc,EAAE;QACvB,MAAMC,YAAY,GAAGlD,YAAG,CAACmD,MAAM,CAACtD,OAAO,EAAEY,MAAM,CAAC;QAEhD,IAAIyC,YAAY,KAAK,IAAI,EAAE;UACzB3C,kBAAM,CAACwB,IAAI,CAAE,yBAAwBmB,YAAa,EAAC,CAAC;UACpD;UACA;UACAP,UAAU,CAACI,IAAI,CAAE,mCAAkCG,YAAa,EAAC,CAAC;UAClEP,UAAU,CAACI,IAAI,CAAE,8BAA6BG,YAAa,EAAC,CAAC;QAC/D;MACF;MAEA,IAAI,CAACjF,IAAI,CAACc,UAAU,EAAE;QACpB,IAAAqE,mBAAK,EAACT,UAAU,EAAEpD,cAAc,CAACqB,SAAS,CAAC;MAC7C;MAEAyC,wBAAwB,CACtBpF,IAAI,EACJwC,MAAM,EACNZ,OAAO,EACPN,cAAc,EACdyB,YAAY,CACb;IACH,CAAC,MAAM;MACLT,kBAAM,CAAC+B,KAAK,CACT,2BAA0B7B,MAAO,wCAAuC,EACzE,GAAGV,OAAO,CACX;IACH;EACF,CAAC,MAAM;IACLQ,kBAAM,CAAC+B,KAAK,CAAC,0CAA0C,CAAC;EAC1D;AACF;AAEA,SAASe,wBAAwB,CAC/BpF,IAAW,EACXqF,cAAsB,EACtBzD,OAAe,EACfN,cAA8B,EAC9ByB,YAAqB,EACrB;EACA,IAAAuC,yBAAgB,EAACtF,IAAI,CAACI,IAAI,EAAEiF,cAAc,CAAC;EAE3C,IAAAE,8BAAqB,EACnBvF,IAAI,EACJ4B,OAAO,EACPyD,cAAc,EACd/D,cAAc,EACdyB,YAAY,CACb;EAED,IAAAyC,6BAAoB,EAACH,cAAc,EAAE/D,cAAc,EAAEM,OAAO,EAAE5B,IAAI,CAAC;AACrE;AAAC,eAEc;EACbyF,IAAI,EAAE,aAAa;EACnBC,WAAW,EACT,yEAAyE;EAC3EC,IAAI,EAAE9F,UAAU;EAChB+F,OAAO,EAAE,CACP,GAAGA,qBAAO,EACV;IACEH,IAAI,EAAE,eAAe;IACrBC,WAAW,EAAE;EACf,CAAC,EACD;IACED,IAAI,EAAE,iBAAiB;IACvBI,OAAO,EAAEpD,OAAO,CAACqD,GAAG,CAACC,cAAc,IAAI,IAAI;IAC3CC,KAAK,EAAEC;EACT,CAAC,EACD;IACER,IAAI,EAAE,qBAAqB;IAC3BC,WAAW,EACT,+EAA+E;IACjFG,OAAO,EAAE,IAAAK,kCAAsB;EACjC,CAAC,EACD;IACET,IAAI,EAAE,kBAAkB;IACxBC,WAAW,EACT,oHAAoH;IACtHG,OAAO,EAAE;EACX,CAAC,EACD;IACEJ,IAAI,EAAE,wBAAwB;IAC9BC,WAAW,EAAE,uDAAuD;IACpEG,OAAO,EAAE;EACX,CAAC,EACD;IACEJ,IAAI,EAAE,0BAA0B;IAChCC,WAAW,EAAE;EACf,CAAC,EACD;IACED,IAAI,EAAE,mBAAmB;IACzBC,WAAW,EACT,sEAAsE,GACtE;EACJ,CAAC,EACD;IACED,IAAI,EAAE,qBAAqB;IAC3BC,WAAW,EACT,uFAAuF,GACvF;EACJ,CAAC,EACD;IACED,IAAI,EAAE,gBAAgB;IACtBC,WAAW,EACT,0FAA0F;IAC5FG,OAAO,EAAE;EACX,CAAC,EACD;IACEJ,IAAI,EAAE,wBAAwB;IAC9BC,WAAW,EACT;EACJ,CAAC,EACD;IACED,IAAI,EAAE,iBAAiB;IACvBC,WAAW,EAAE,wDAAwD;IACrEM,KAAK,EAAEC;EACT,CAAC;AAEL,CAAC;AAAA"}