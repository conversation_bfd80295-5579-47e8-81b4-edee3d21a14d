!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t():"function"==typeof define&&define.amd?define(t):(e="undefined"!=typeof globalThis?globalThis:e||self).i18next=t()}(this,(function(){"use strict";const e=e=>"string"==typeof e,t=()=>{let e,t;const s=new Promise(((s,i)=>{e=s,t=i}));return s.resolve=e,s.reject=t,s},s=e=>null==e?"":""+e,i=/###/g,o=e=>e&&e.indexOf("###")>-1?e.replace(i,"."):e,n=t=>!t||e(t),r=(t,s,i)=>{const r=e(s)?s.split("."):s;let a=0;for(;a<r.length-1;){if(n(t))return{};const e=o(r[a]);!t[e]&&i&&(t[e]=new i),t=Object.prototype.hasOwnProperty.call(t,e)?t[e]:{},++a}return n(t)?{}:{obj:t,k:o(r[a])}},a=(e,t,s)=>{const{obj:i,k:o}=r(e,t,Object);if(void 0!==i||1===t.length)return void(i[o]=s);let n=t[t.length-1],a=t.slice(0,t.length-1),l=r(e,a,Object);for(;void 0===l.obj&&a.length;)n=`${a[a.length-1]}.${n}`,a=a.slice(0,a.length-1),l=r(e,a,Object),l&&l.obj&&void 0!==l.obj[`${l.k}.${n}`]&&(l.obj=void 0);l.obj[`${l.k}.${n}`]=s},l=(e,t)=>{const{obj:s,k:i}=r(e,t);if(s)return s[i]},h=(t,s,i)=>{for(const o in s)"__proto__"!==o&&"constructor"!==o&&(o in t?e(t[o])||t[o]instanceof String||e(s[o])||s[o]instanceof String?i&&(t[o]=s[o]):h(t[o],s[o],i):t[o]=s[o]);return t},u=e=>e.replace(/[\-\[\]\/\{\}\(\)\*\+\?\.\\\^\$\|]/g,"\\$&");var p={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;","/":"&#x2F;"};const c=t=>e(t)?t.replace(/[&<>"'\/]/g,(e=>p[e])):t;const g=[" ",",","?","!",";"],d=new class{constructor(e){this.capacity=e,this.regExpMap=new Map,this.regExpQueue=[]}getRegExp(e){const t=this.regExpMap.get(e);if(void 0!==t)return t;const s=new RegExp(e);return this.regExpQueue.length===this.capacity&&this.regExpMap.delete(this.regExpQueue.shift()),this.regExpMap.set(e,s),this.regExpQueue.push(e),s}}(20),f=function(e,t){let s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:".";if(!e)return;if(e[t])return e[t];const i=t.split(s);let o=e;for(let e=0;e<i.length;){if(!o||"object"!=typeof o)return;let t,n="";for(let r=e;r<i.length;++r)if(r!==e&&(n+=s),n+=i[r],t=o[n],void 0!==t){if(["string","number","boolean"].indexOf(typeof t)>-1&&r<i.length-1)continue;e+=r-e+1;break}o=t}return o},m=e=>e&&e.replace("_","-"),y={type:"logger",log(e){this.output("log",e)},warn(e){this.output("warn",e)},error(e){this.output("error",e)},output(e,t){console&&console[e]&&console[e].apply(console,t)}};class v{constructor(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};this.init(e,t)}init(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};this.prefix=t.prefix||"i18next:",this.logger=e||y,this.options=t,this.debug=t.debug}log(){for(var e=arguments.length,t=new Array(e),s=0;s<e;s++)t[s]=arguments[s];return this.forward(t,"log","",!0)}warn(){for(var e=arguments.length,t=new Array(e),s=0;s<e;s++)t[s]=arguments[s];return this.forward(t,"warn","",!0)}error(){for(var e=arguments.length,t=new Array(e),s=0;s<e;s++)t[s]=arguments[s];return this.forward(t,"error","")}deprecate(){for(var e=arguments.length,t=new Array(e),s=0;s<e;s++)t[s]=arguments[s];return this.forward(t,"warn","WARNING DEPRECATED: ",!0)}forward(t,s,i,o){return o&&!this.debug?null:(e(t[0])&&(t[0]=`${i}${this.prefix} ${t[0]}`),this.logger[s](t))}create(e){return new v(this.logger,{prefix:`${this.prefix}:${e}:`,...this.options})}clone(e){return(e=e||this.options).prefix=e.prefix||this.prefix,new v(this.logger,e)}}var b=new v;class x{constructor(){this.observers={}}on(e,t){return e.split(" ").forEach((e=>{this.observers[e]||(this.observers[e]=new Map);const s=this.observers[e].get(t)||0;this.observers[e].set(t,s+1)})),this}off(e,t){this.observers[e]&&(t?this.observers[e].delete(t):delete this.observers[e])}emit(e){for(var t=arguments.length,s=new Array(t>1?t-1:0),i=1;i<t;i++)s[i-1]=arguments[i];if(this.observers[e]){Array.from(this.observers[e].entries()).forEach((e=>{let[t,i]=e;for(let e=0;e<i;e++)t(...s)}))}if(this.observers["*"]){Array.from(this.observers["*"].entries()).forEach((t=>{let[i,o]=t;for(let t=0;t<o;t++)i.apply(i,[e,...s])}))}}}class k extends x{constructor(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{ns:["translation"],defaultNS:"translation"};super(),this.data=e||{},this.options=t,void 0===this.options.keySeparator&&(this.options.keySeparator="."),void 0===this.options.ignoreJSONStructure&&(this.options.ignoreJSONStructure=!0)}addNamespaces(e){this.options.ns.indexOf(e)<0&&this.options.ns.push(e)}removeNamespaces(e){const t=this.options.ns.indexOf(e);t>-1&&this.options.ns.splice(t,1)}getResource(t,s,i){let o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{};const n=void 0!==o.keySeparator?o.keySeparator:this.options.keySeparator,r=void 0!==o.ignoreJSONStructure?o.ignoreJSONStructure:this.options.ignoreJSONStructure;let a;t.indexOf(".")>-1?a=t.split("."):(a=[t,s],i&&(Array.isArray(i)?a.push(...i):e(i)&&n?a.push(...i.split(n)):a.push(i)));const h=l(this.data,a);return!h&&!s&&!i&&t.indexOf(".")>-1&&(t=a[0],s=a[1],i=a.slice(2).join(".")),!h&&r&&e(i)?f(this.data&&this.data[t]&&this.data[t][s],i,n):h}addResource(e,t,s,i){let o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:{silent:!1};const n=void 0!==o.keySeparator?o.keySeparator:this.options.keySeparator;let r=[e,t];s&&(r=r.concat(n?s.split(n):s)),e.indexOf(".")>-1&&(r=e.split("."),i=t,t=r[1]),this.addNamespaces(t),a(this.data,r,i),o.silent||this.emit("added",e,t,s,i)}addResources(t,s,i){let o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{silent:!1};for(const o in i)(e(i[o])||Array.isArray(i[o]))&&this.addResource(t,s,o,i[o],{silent:!0});o.silent||this.emit("added",t,s,i)}addResourceBundle(e,t,s,i,o){let n=arguments.length>5&&void 0!==arguments[5]?arguments[5]:{silent:!1,skipCopy:!1},r=[e,t];e.indexOf(".")>-1&&(r=e.split("."),i=s,s=t,t=r[1]),this.addNamespaces(t);let u=l(this.data,r)||{};n.skipCopy||(s=JSON.parse(JSON.stringify(s))),i?h(u,s,o):u={...u,...s},a(this.data,r,u),n.silent||this.emit("added",e,t,s)}removeResourceBundle(e,t){this.hasResourceBundle(e,t)&&delete this.data[e][t],this.removeNamespaces(t),this.emit("removed",e,t)}hasResourceBundle(e,t){return void 0!==this.getResource(e,t)}getResourceBundle(e,t){return t||(t=this.options.defaultNS),"v1"===this.options.compatibilityAPI?{...this.getResource(e,t)}:this.getResource(e,t)}getDataByLanguage(e){return this.data[e]}hasLanguageSomeTranslations(e){const t=this.getDataByLanguage(e);return!!(t&&Object.keys(t)||[]).find((e=>t[e]&&Object.keys(t[e]).length>0))}toJSON(){return this.data}}var S={processors:{},addPostProcessor(e){this.processors[e.name]=e},handle(e,t,s,i,o){return e.forEach((e=>{this.processors[e]&&(t=this.processors[e].process(t,s,i,o))})),t}};const O={};class L extends x{constructor(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};var s,i;super(),s=e,i=this,["resourceStore","languageUtils","pluralResolver","interpolator","backendConnector","i18nFormat","utils"].forEach((e=>{s[e]&&(i[e]=s[e])})),this.options=t,void 0===this.options.keySeparator&&(this.options.keySeparator="."),this.logger=b.create("translator")}changeLanguage(e){e&&(this.language=e)}exists(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{interpolation:{}};if(null==e)return!1;const s=this.resolve(e,t);return s&&void 0!==s.res}extractFromKey(t,s){let i=void 0!==s.nsSeparator?s.nsSeparator:this.options.nsSeparator;void 0===i&&(i=":");const o=void 0!==s.keySeparator?s.keySeparator:this.options.keySeparator;let n=s.ns||this.options.defaultNS||[];const r=i&&t.indexOf(i)>-1,a=!(this.options.userDefinedKeySeparator||s.keySeparator||this.options.userDefinedNsSeparator||s.nsSeparator||((e,t,s)=>{t=t||"",s=s||"";const i=g.filter((e=>t.indexOf(e)<0&&s.indexOf(e)<0));if(0===i.length)return!0;const o=d.getRegExp(`(${i.map((e=>"?"===e?"\\?":e)).join("|")})`);let n=!o.test(e);if(!n){const t=e.indexOf(s);t>0&&!o.test(e.substring(0,t))&&(n=!0)}return n})(t,i,o));if(r&&!a){const s=t.match(this.interpolator.nestingRegexp);if(s&&s.length>0)return{key:t,namespaces:e(n)?[n]:n};const r=t.split(i);(i!==o||i===o&&this.options.ns.indexOf(r[0])>-1)&&(n=r.shift()),t=r.join(o)}return{key:t,namespaces:e(n)?[n]:n}}translate(t,s,i){if("object"!=typeof s&&this.options.overloadTranslationOptionHandler&&(s=this.options.overloadTranslationOptionHandler(arguments)),"object"==typeof s&&(s={...s}),s||(s={}),null==t)return"";Array.isArray(t)||(t=[String(t)]);const o=void 0!==s.returnDetails?s.returnDetails:this.options.returnDetails,n=void 0!==s.keySeparator?s.keySeparator:this.options.keySeparator,{key:r,namespaces:a}=this.extractFromKey(t[t.length-1],s),l=a[a.length-1],h=s.lng||this.language,u=s.appendNamespaceToCIMode||this.options.appendNamespaceToCIMode;if(h&&"cimode"===h.toLowerCase()){if(u){const e=s.nsSeparator||this.options.nsSeparator;return o?{res:`${l}${e}${r}`,usedKey:r,exactUsedKey:r,usedLng:h,usedNS:l,usedParams:this.getUsedParamsDetails(s)}:`${l}${e}${r}`}return o?{res:r,usedKey:r,exactUsedKey:r,usedLng:h,usedNS:l,usedParams:this.getUsedParamsDetails(s)}:r}const p=this.resolve(t,s);let c=p&&p.res;const g=p&&p.usedKey||r,d=p&&p.exactUsedKey||r,f=Object.prototype.toString.apply(c),m=void 0!==s.joinArrays?s.joinArrays:this.options.joinArrays,y=!this.i18nFormat||this.i18nFormat.handleAsObject,v=!e(c)&&"boolean"!=typeof c&&"number"!=typeof c;if(!(y&&c&&v&&["[object Number]","[object Function]","[object RegExp]"].indexOf(f)<0)||e(m)&&Array.isArray(c))if(y&&e(m)&&Array.isArray(c))c=c.join(m),c&&(c=this.extendTranslation(c,t,s,i));else{let o=!1,a=!1;const u=void 0!==s.count&&!e(s.count),g=L.hasDefaultValue(s),d=u?this.pluralResolver.getSuffix(h,s.count,s):"",f=s.ordinal&&u?this.pluralResolver.getSuffix(h,s.count,{ordinal:!1}):"",m=u&&!s.ordinal&&0===s.count&&this.pluralResolver.shouldUseIntlApi(),y=m&&s[`defaultValue${this.options.pluralSeparator}zero`]||s[`defaultValue${d}`]||s[`defaultValue${f}`]||s.defaultValue;!this.isValidLookup(c)&&g&&(o=!0,c=y),this.isValidLookup(c)||(a=!0,c=r);const v=(s.missingKeyNoValueFallbackToKey||this.options.missingKeyNoValueFallbackToKey)&&a?void 0:c,b=g&&y!==c&&this.options.updateMissing;if(a||o||b){if(this.logger.log(b?"updateKey":"missingKey",h,l,r,b?y:c),n){const e=this.resolve(r,{...s,keySeparator:!1});e&&e.res&&this.logger.warn("Seems the loaded translations were in flat JSON format instead of nested. Either set keySeparator: false on init or make sure your translations are published in nested format.")}let e=[];const t=this.languageUtils.getFallbackCodes(this.options.fallbackLng,s.lng||this.language);if("fallback"===this.options.saveMissingTo&&t&&t[0])for(let s=0;s<t.length;s++)e.push(t[s]);else"all"===this.options.saveMissingTo?e=this.languageUtils.toResolveHierarchy(s.lng||this.language):e.push(s.lng||this.language);const i=(e,t,i)=>{const o=g&&i!==c?i:v;this.options.missingKeyHandler?this.options.missingKeyHandler(e,l,t,o,b,s):this.backendConnector&&this.backendConnector.saveMissing&&this.backendConnector.saveMissing(e,l,t,o,b,s),this.emit("missingKey",e,l,t,c)};this.options.saveMissing&&(this.options.saveMissingPlurals&&u?e.forEach((e=>{const t=this.pluralResolver.getSuffixes(e,s);m&&s[`defaultValue${this.options.pluralSeparator}zero`]&&t.indexOf(`${this.options.pluralSeparator}zero`)<0&&t.push(`${this.options.pluralSeparator}zero`),t.forEach((t=>{i([e],r+t,s[`defaultValue${t}`]||y)}))})):i(e,r,y))}c=this.extendTranslation(c,t,s,p,i),a&&c===r&&this.options.appendNamespaceToMissingKey&&(c=`${l}:${r}`),(a||o)&&this.options.parseMissingKeyHandler&&(c="v1"!==this.options.compatibilityAPI?this.options.parseMissingKeyHandler(this.options.appendNamespaceToMissingKey?`${l}:${r}`:r,o?c:void 0):this.options.parseMissingKeyHandler(c))}else{if(!s.returnObjects&&!this.options.returnObjects){this.options.returnedObjectHandler||this.logger.warn("accessing an object - but returnObjects options is not enabled!");const e=this.options.returnedObjectHandler?this.options.returnedObjectHandler(g,c,{...s,ns:a}):`key '${r} (${this.language})' returned an object instead of string.`;return o?(p.res=e,p.usedParams=this.getUsedParamsDetails(s),p):e}if(n){const e=Array.isArray(c),t=e?[]:{},i=e?d:g;for(const e in c)if(Object.prototype.hasOwnProperty.call(c,e)){const o=`${i}${n}${e}`;t[e]=this.translate(o,{...s,joinArrays:!1,ns:a}),t[e]===o&&(t[e]=c[e])}c=t}}return o?(p.res=c,p.usedParams=this.getUsedParamsDetails(s),p):c}extendTranslation(t,s,i,o,n){var r=this;if(this.i18nFormat&&this.i18nFormat.parse)t=this.i18nFormat.parse(t,{...this.options.interpolation.defaultVariables,...i},i.lng||this.language||o.usedLng,o.usedNS,o.usedKey,{resolved:o});else if(!i.skipInterpolation){i.interpolation&&this.interpolator.init({...i,interpolation:{...this.options.interpolation,...i.interpolation}});const a=e(t)&&(i&&i.interpolation&&void 0!==i.interpolation.skipOnVariables?i.interpolation.skipOnVariables:this.options.interpolation.skipOnVariables);let l;if(a){const e=t.match(this.interpolator.nestingRegexp);l=e&&e.length}let h=i.replace&&!e(i.replace)?i.replace:i;if(this.options.interpolation.defaultVariables&&(h={...this.options.interpolation.defaultVariables,...h}),t=this.interpolator.interpolate(t,h,i.lng||this.language||o.usedLng,i),a){const e=t.match(this.interpolator.nestingRegexp);l<(e&&e.length)&&(i.nest=!1)}!i.lng&&"v1"!==this.options.compatibilityAPI&&o&&o.res&&(i.lng=this.language||o.usedLng),!1!==i.nest&&(t=this.interpolator.nest(t,(function(){for(var e=arguments.length,t=new Array(e),o=0;o<e;o++)t[o]=arguments[o];return n&&n[0]===t[0]&&!i.context?(r.logger.warn(`It seems you are nesting recursively key: ${t[0]} in key: ${s[0]}`),null):r.translate(...t,s)}),i)),i.interpolation&&this.interpolator.reset()}const a=i.postProcess||this.options.postProcess,l=e(a)?[a]:a;return null!=t&&l&&l.length&&!1!==i.applyPostProcessor&&(t=S.handle(l,t,s,this.options&&this.options.postProcessPassResolved?{i18nResolved:{...o,usedParams:this.getUsedParamsDetails(i)},...i}:i,this)),t}resolve(t){let s,i,o,n,r,a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return e(t)&&(t=[t]),t.forEach((t=>{if(this.isValidLookup(s))return;const l=this.extractFromKey(t,a),h=l.key;i=h;let u=l.namespaces;this.options.fallbackNS&&(u=u.concat(this.options.fallbackNS));const p=void 0!==a.count&&!e(a.count),c=p&&!a.ordinal&&0===a.count&&this.pluralResolver.shouldUseIntlApi(),g=void 0!==a.context&&(e(a.context)||"number"==typeof a.context)&&""!==a.context,d=a.lngs?a.lngs:this.languageUtils.toResolveHierarchy(a.lng||this.language,a.fallbackLng);u.forEach((e=>{this.isValidLookup(s)||(r=e,!O[`${d[0]}-${e}`]&&this.utils&&this.utils.hasLoadedNamespace&&!this.utils.hasLoadedNamespace(r)&&(O[`${d[0]}-${e}`]=!0,this.logger.warn(`key "${i}" for languages "${d.join(", ")}" won't get resolved as namespace "${r}" was not yet loaded`,"This means something IS WRONG in your setup. You access the t function before i18next.init / i18next.loadNamespace / i18next.changeLanguage was done. Wait for the callback or Promise to resolve before accessing it!!!")),d.forEach((t=>{if(this.isValidLookup(s))return;n=t;const i=[h];if(this.i18nFormat&&this.i18nFormat.addLookupKeys)this.i18nFormat.addLookupKeys(i,h,t,e,a);else{let e;p&&(e=this.pluralResolver.getSuffix(t,a.count,a));const s=`${this.options.pluralSeparator}zero`,o=`${this.options.pluralSeparator}ordinal${this.options.pluralSeparator}`;if(p&&(i.push(h+e),a.ordinal&&0===e.indexOf(o)&&i.push(h+e.replace(o,this.options.pluralSeparator)),c&&i.push(h+s)),g){const t=`${h}${this.options.contextSeparator}${a.context}`;i.push(t),p&&(i.push(t+e),a.ordinal&&0===e.indexOf(o)&&i.push(t+e.replace(o,this.options.pluralSeparator)),c&&i.push(t+s))}}let r;for(;r=i.pop();)this.isValidLookup(s)||(o=r,s=this.getResource(t,e,r,a))})))}))})),{res:s,usedKey:i,exactUsedKey:o,usedLng:n,usedNS:r}}isValidLookup(e){return!(void 0===e||!this.options.returnNull&&null===e||!this.options.returnEmptyString&&""===e)}getResource(e,t,s){let i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{};return this.i18nFormat&&this.i18nFormat.getResource?this.i18nFormat.getResource(e,t,s,i):this.resourceStore.getResource(e,t,s,i)}getUsedParamsDetails(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};const s=["defaultValue","ordinal","context","replace","lng","lngs","fallbackLng","ns","keySeparator","nsSeparator","returnObjects","returnDetails","joinArrays","postProcess","interpolation"],i=t.replace&&!e(t.replace);let o=i?t.replace:t;if(i&&void 0!==t.count&&(o.count=t.count),this.options.interpolation.defaultVariables&&(o={...this.options.interpolation.defaultVariables,...o}),!i){o={...o};for(const e of s)delete o[e]}return o}static hasDefaultValue(e){const t="defaultValue";for(const s in e)if(Object.prototype.hasOwnProperty.call(e,s)&&t===s.substring(0,12)&&void 0!==e[s])return!0;return!1}}const w=e=>e.charAt(0).toUpperCase()+e.slice(1);class N{constructor(e){this.options=e,this.supportedLngs=this.options.supportedLngs||!1,this.logger=b.create("languageUtils")}getScriptPartFromCode(e){if(!(e=m(e))||e.indexOf("-")<0)return null;const t=e.split("-");return 2===t.length?null:(t.pop(),"x"===t[t.length-1].toLowerCase()?null:this.formatLanguageCode(t.join("-")))}getLanguagePartFromCode(e){if(!(e=m(e))||e.indexOf("-")<0)return e;const t=e.split("-");return this.formatLanguageCode(t[0])}formatLanguageCode(t){if(e(t)&&t.indexOf("-")>-1){if("undefined"!=typeof Intl&&void 0!==Intl.getCanonicalLocales)try{let e=Intl.getCanonicalLocales(t)[0];if(e&&this.options.lowerCaseLng&&(e=e.toLowerCase()),e)return e}catch(e){}const e=["hans","hant","latn","cyrl","cans","mong","arab"];let s=t.split("-");return this.options.lowerCaseLng?s=s.map((e=>e.toLowerCase())):2===s.length?(s[0]=s[0].toLowerCase(),s[1]=s[1].toUpperCase(),e.indexOf(s[1].toLowerCase())>-1&&(s[1]=w(s[1].toLowerCase()))):3===s.length&&(s[0]=s[0].toLowerCase(),2===s[1].length&&(s[1]=s[1].toUpperCase()),"sgn"!==s[0]&&2===s[2].length&&(s[2]=s[2].toUpperCase()),e.indexOf(s[1].toLowerCase())>-1&&(s[1]=w(s[1].toLowerCase())),e.indexOf(s[2].toLowerCase())>-1&&(s[2]=w(s[2].toLowerCase()))),s.join("-")}return this.options.cleanCode||this.options.lowerCaseLng?t.toLowerCase():t}isSupportedCode(e){return("languageOnly"===this.options.load||this.options.nonExplicitSupportedLngs)&&(e=this.getLanguagePartFromCode(e)),!this.supportedLngs||!this.supportedLngs.length||this.supportedLngs.indexOf(e)>-1}getBestMatchFromCodes(e){if(!e)return null;let t;return e.forEach((e=>{if(t)return;const s=this.formatLanguageCode(e);this.options.supportedLngs&&!this.isSupportedCode(s)||(t=s)})),!t&&this.options.supportedLngs&&e.forEach((e=>{if(t)return;const s=this.getLanguagePartFromCode(e);if(this.isSupportedCode(s))return t=s;t=this.options.supportedLngs.find((e=>e===s?e:e.indexOf("-")<0&&s.indexOf("-")<0?void 0:e.indexOf("-")>0&&s.indexOf("-")<0&&e.substring(0,e.indexOf("-"))===s||0===e.indexOf(s)&&s.length>1?e:void 0))})),t||(t=this.getFallbackCodes(this.options.fallbackLng)[0]),t}getFallbackCodes(t,s){if(!t)return[];if("function"==typeof t&&(t=t(s)),e(t)&&(t=[t]),Array.isArray(t))return t;if(!s)return t.default||[];let i=t[s];return i||(i=t[this.getScriptPartFromCode(s)]),i||(i=t[this.formatLanguageCode(s)]),i||(i=t[this.getLanguagePartFromCode(s)]),i||(i=t.default),i||[]}toResolveHierarchy(t,s){const i=this.getFallbackCodes(s||this.options.fallbackLng||[],t),o=[],n=e=>{e&&(this.isSupportedCode(e)?o.push(e):this.logger.warn(`rejecting language code not found in supportedLngs: ${e}`))};return e(t)&&(t.indexOf("-")>-1||t.indexOf("_")>-1)?("languageOnly"!==this.options.load&&n(this.formatLanguageCode(t)),"languageOnly"!==this.options.load&&"currentOnly"!==this.options.load&&n(this.getScriptPartFromCode(t)),"currentOnly"!==this.options.load&&n(this.getLanguagePartFromCode(t))):e(t)&&n(this.formatLanguageCode(t)),i.forEach((e=>{o.indexOf(e)<0&&n(this.formatLanguageCode(e))})),o}}let R=[{lngs:["ach","ak","am","arn","br","fil","gun","ln","mfe","mg","mi","oc","pt","pt-BR","tg","tl","ti","tr","uz","wa"],nr:[1,2],fc:1},{lngs:["af","an","ast","az","bg","bn","ca","da","de","dev","el","en","eo","es","et","eu","fi","fo","fur","fy","gl","gu","ha","hi","hu","hy","ia","it","kk","kn","ku","lb","mai","ml","mn","mr","nah","nap","nb","ne","nl","nn","no","nso","pa","pap","pms","ps","pt-PT","rm","sco","se","si","so","son","sq","sv","sw","ta","te","tk","ur","yo"],nr:[1,2],fc:2},{lngs:["ay","bo","cgg","fa","ht","id","ja","jbo","ka","km","ko","ky","lo","ms","sah","su","th","tt","ug","vi","wo","zh"],nr:[1],fc:3},{lngs:["be","bs","cnr","dz","hr","ru","sr","uk"],nr:[1,2,5],fc:4},{lngs:["ar"],nr:[0,1,2,3,11,100],fc:5},{lngs:["cs","sk"],nr:[1,2,5],fc:6},{lngs:["csb","pl"],nr:[1,2,5],fc:7},{lngs:["cy"],nr:[1,2,3,8],fc:8},{lngs:["fr"],nr:[1,2],fc:9},{lngs:["ga"],nr:[1,2,3,7,11],fc:10},{lngs:["gd"],nr:[1,2,3,20],fc:11},{lngs:["is"],nr:[1,2],fc:12},{lngs:["jv"],nr:[0,1],fc:13},{lngs:["kw"],nr:[1,2,3,4],fc:14},{lngs:["lt"],nr:[1,2,10],fc:15},{lngs:["lv"],nr:[1,2,0],fc:16},{lngs:["mk"],nr:[1,2],fc:17},{lngs:["mnk"],nr:[0,1,2],fc:18},{lngs:["mt"],nr:[1,2,11,20],fc:19},{lngs:["or"],nr:[2,1],fc:2},{lngs:["ro"],nr:[1,2,20],fc:20},{lngs:["sl"],nr:[5,1,2,3],fc:21},{lngs:["he","iw"],nr:[1,2,20,21],fc:22}],C={1:e=>Number(e>1),2:e=>Number(1!=e),3:e=>0,4:e=>Number(e%10==1&&e%100!=11?0:e%10>=2&&e%10<=4&&(e%100<10||e%100>=20)?1:2),5:e=>Number(0==e?0:1==e?1:2==e?2:e%100>=3&&e%100<=10?3:e%100>=11?4:5),6:e=>Number(1==e?0:e>=2&&e<=4?1:2),7:e=>Number(1==e?0:e%10>=2&&e%10<=4&&(e%100<10||e%100>=20)?1:2),8:e=>Number(1==e?0:2==e?1:8!=e&&11!=e?2:3),9:e=>Number(e>=2),10:e=>Number(1==e?0:2==e?1:e<7?2:e<11?3:4),11:e=>Number(1==e||11==e?0:2==e||12==e?1:e>2&&e<20?2:3),12:e=>Number(e%10!=1||e%100==11),13:e=>Number(0!==e),14:e=>Number(1==e?0:2==e?1:3==e?2:3),15:e=>Number(e%10==1&&e%100!=11?0:e%10>=2&&(e%100<10||e%100>=20)?1:2),16:e=>Number(e%10==1&&e%100!=11?0:0!==e?1:2),17:e=>Number(1==e||e%10==1&&e%100!=11?0:1),18:e=>Number(0==e?0:1==e?1:2),19:e=>Number(1==e?0:0==e||e%100>1&&e%100<11?1:e%100>10&&e%100<20?2:3),20:e=>Number(1==e?0:0==e||e%100>0&&e%100<20?1:2),21:e=>Number(e%100==1?1:e%100==2?2:e%100==3||e%100==4?3:0),22:e=>Number(1==e?0:2==e?1:(e<0||e>10)&&e%10==0?2:3)};const $=["v1","v2","v3"],P=["v4"],j={zero:0,one:1,two:2,few:3,many:4,other:5};class E{constructor(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};this.languageUtils=e,this.options=t,this.logger=b.create("pluralResolver"),this.options.compatibilityJSON&&!P.includes(this.options.compatibilityJSON)||"undefined"!=typeof Intl&&Intl.PluralRules||(this.options.compatibilityJSON="v3",this.logger.error("Your environment seems not to be Intl API compatible, use an Intl.PluralRules polyfill. Will fallback to the compatibilityJSON v3 format handling.")),this.rules=(()=>{const e={};return R.forEach((t=>{t.lngs.forEach((s=>{e[s]={numbers:t.nr,plurals:C[t.fc]}}))})),e})(),this.pluralRulesCache={}}addRule(e,t){this.rules[e]=t}clearCache(){this.pluralRulesCache={}}getRule(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(this.shouldUseIntlApi()){const s=m("dev"===e?"en":e),i=t.ordinal?"ordinal":"cardinal",o=JSON.stringify({cleanedCode:s,type:i});if(o in this.pluralRulesCache)return this.pluralRulesCache[o];let n;try{n=new Intl.PluralRules(s,{type:i})}catch(s){if(!e.match(/-|_/))return;const i=this.languageUtils.getLanguagePartFromCode(e);n=this.getRule(i,t)}return this.pluralRulesCache[o]=n,n}return this.rules[e]||this.rules[this.languageUtils.getLanguagePartFromCode(e)]}needsPlural(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const s=this.getRule(e,t);return this.shouldUseIntlApi()?s&&s.resolvedOptions().pluralCategories.length>1:s&&s.numbers.length>1}getPluralFormsOfKey(e,t){let s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return this.getSuffixes(e,s).map((e=>`${t}${e}`))}getSuffixes(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const s=this.getRule(e,t);return s?this.shouldUseIntlApi()?s.resolvedOptions().pluralCategories.sort(((e,t)=>j[e]-j[t])).map((e=>`${this.options.prepend}${t.ordinal?`ordinal${this.options.prepend}`:""}${e}`)):s.numbers.map((s=>this.getSuffix(e,s,t))):[]}getSuffix(e,t){let s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};const i=this.getRule(e,s);return i?this.shouldUseIntlApi()?`${this.options.prepend}${s.ordinal?`ordinal${this.options.prepend}`:""}${i.select(t)}`:this.getSuffixRetroCompatible(i,t):(this.logger.warn(`no plural rule found for: ${e}`),"")}getSuffixRetroCompatible(e,t){const s=e.noAbs?e.plurals(t):e.plurals(Math.abs(t));let i=e.numbers[s];this.options.simplifyPluralSuffix&&2===e.numbers.length&&1===e.numbers[0]&&(2===i?i="plural":1===i&&(i=""));const o=()=>this.options.prepend&&i.toString()?this.options.prepend+i.toString():i.toString();return"v1"===this.options.compatibilityJSON?1===i?"":"number"==typeof i?`_plural_${i.toString()}`:o():"v2"===this.options.compatibilityJSON||this.options.simplifyPluralSuffix&&2===e.numbers.length&&1===e.numbers[0]?o():this.options.prepend&&s.toString()?this.options.prepend+s.toString():s.toString()}shouldUseIntlApi(){return!$.includes(this.options.compatibilityJSON)}}const I=function(t,s,i){let o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:".",n=!(arguments.length>4&&void 0!==arguments[4])||arguments[4],r=((e,t,s)=>{const i=l(e,s);return void 0!==i?i:l(t,s)})(t,s,i);return!r&&n&&e(i)&&(r=f(t,i,o),void 0===r&&(r=f(s,i,o))),r},F=e=>e.replace(/\$/g,"$$$$");class A{constructor(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};this.logger=b.create("interpolator"),this.options=e,this.format=e.interpolation&&e.interpolation.format||(e=>e),this.init(e)}init(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};e.interpolation||(e.interpolation={escapeValue:!0});const{escape:t,escapeValue:s,useRawValueToEscape:i,prefix:o,prefixEscaped:n,suffix:r,suffixEscaped:a,formatSeparator:l,unescapeSuffix:h,unescapePrefix:p,nestingPrefix:g,nestingPrefixEscaped:d,nestingSuffix:f,nestingSuffixEscaped:m,nestingOptionsSeparator:y,maxReplaces:v,alwaysFormat:b}=e.interpolation;this.escape=void 0!==t?t:c,this.escapeValue=void 0===s||s,this.useRawValueToEscape=void 0!==i&&i,this.prefix=o?u(o):n||"{{",this.suffix=r?u(r):a||"}}",this.formatSeparator=l||",",this.unescapePrefix=h?"":p||"-",this.unescapeSuffix=this.unescapePrefix?"":h||"",this.nestingPrefix=g?u(g):d||u("$t("),this.nestingSuffix=f?u(f):m||u(")"),this.nestingOptionsSeparator=y||",",this.maxReplaces=v||1e3,this.alwaysFormat=void 0!==b&&b,this.resetRegExp()}reset(){this.options&&this.init(this.options)}resetRegExp(){const e=(e,t)=>e&&e.source===t?(e.lastIndex=0,e):new RegExp(t,"g");this.regexp=e(this.regexp,`${this.prefix}(.+?)${this.suffix}`),this.regexpUnescape=e(this.regexpUnescape,`${this.prefix}${this.unescapePrefix}(.+?)${this.unescapeSuffix}${this.suffix}`),this.nestingRegexp=e(this.nestingRegexp,`${this.nestingPrefix}(.+?)${this.nestingSuffix}`)}interpolate(t,i,o,n){let r,a,l;const h=this.options&&this.options.interpolation&&this.options.interpolation.defaultVariables||{},u=e=>{if(e.indexOf(this.formatSeparator)<0){const t=I(i,h,e,this.options.keySeparator,this.options.ignoreJSONStructure);return this.alwaysFormat?this.format(t,void 0,o,{...n,...i,interpolationkey:e}):t}const t=e.split(this.formatSeparator),s=t.shift().trim(),r=t.join(this.formatSeparator).trim();return this.format(I(i,h,s,this.options.keySeparator,this.options.ignoreJSONStructure),r,o,{...n,...i,interpolationkey:s})};this.resetRegExp();const p=n&&n.missingInterpolationHandler||this.options.missingInterpolationHandler,c=n&&n.interpolation&&void 0!==n.interpolation.skipOnVariables?n.interpolation.skipOnVariables:this.options.interpolation.skipOnVariables;return[{regex:this.regexpUnescape,safeValue:e=>F(e)},{regex:this.regexp,safeValue:e=>this.escapeValue?F(this.escape(e)):F(e)}].forEach((i=>{for(l=0;r=i.regex.exec(t);){const o=r[1].trim();if(a=u(o),void 0===a)if("function"==typeof p){const s=p(t,r,n);a=e(s)?s:""}else if(n&&Object.prototype.hasOwnProperty.call(n,o))a="";else{if(c){a=r[0];continue}this.logger.warn(`missed to pass in variable ${o} for interpolating ${t}`),a=""}else e(a)||this.useRawValueToEscape||(a=s(a));const h=i.safeValue(a);if(t=t.replace(r[0],h),c?(i.regex.lastIndex+=a.length,i.regex.lastIndex-=r[0].length):i.regex.lastIndex=0,l++,l>=this.maxReplaces)break}})),t}nest(t,i){let o,n,r,a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};const l=(e,t)=>{const s=this.nestingOptionsSeparator;if(e.indexOf(s)<0)return e;const i=e.split(new RegExp(`${s}[ ]*{`));let o=`{${i[1]}`;e=i[0],o=this.interpolate(o,r);const n=o.match(/'/g),a=o.match(/"/g);(n&&n.length%2==0&&!a||a.length%2!=0)&&(o=o.replace(/'/g,'"'));try{r=JSON.parse(o),t&&(r={...t,...r})}catch(t){return this.logger.warn(`failed parsing options string in nesting for key ${e}`,t),`${e}${s}${o}`}return r.defaultValue&&r.defaultValue.indexOf(this.prefix)>-1&&delete r.defaultValue,e};for(;o=this.nestingRegexp.exec(t);){let h=[];r={...a},r=r.replace&&!e(r.replace)?r.replace:r,r.applyPostProcessor=!1,delete r.defaultValue;let u=!1;if(-1!==o[0].indexOf(this.formatSeparator)&&!/{.*}/.test(o[1])){const e=o[1].split(this.formatSeparator).map((e=>e.trim()));o[1]=e.shift(),h=e,u=!0}if(n=i(l.call(this,o[1].trim(),r),r),n&&o[0]===t&&!e(n))return n;e(n)||(n=s(n)),n||(this.logger.warn(`missed to resolve ${o[1]} for nesting ${t}`),n=""),u&&(n=h.reduce(((e,t)=>this.format(e,t,a.lng,{...a,interpolationkey:o[1].trim()})),n.trim())),t=t.replace(o[0],n),this.regexp.lastIndex=0}return t}}const V=e=>{const t={};return(s,i,o)=>{let n=o;o&&o.interpolationkey&&o.formatParams&&o.formatParams[o.interpolationkey]&&o[o.interpolationkey]&&(n={...n,[o.interpolationkey]:void 0});const r=i+JSON.stringify(n);let a=t[r];return a||(a=e(m(i),o),t[r]=a),a(s)}};class D{constructor(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};this.logger=b.create("formatter"),this.options=e,this.formats={number:V(((e,t)=>{const s=new Intl.NumberFormat(e,{...t});return e=>s.format(e)})),currency:V(((e,t)=>{const s=new Intl.NumberFormat(e,{...t,style:"currency"});return e=>s.format(e)})),datetime:V(((e,t)=>{const s=new Intl.DateTimeFormat(e,{...t});return e=>s.format(e)})),relativetime:V(((e,t)=>{const s=new Intl.RelativeTimeFormat(e,{...t});return e=>s.format(e,t.range||"day")})),list:V(((e,t)=>{const s=new Intl.ListFormat(e,{...t});return e=>s.format(e)}))},this.init(e)}init(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{interpolation:{}};this.formatSeparator=t.interpolation.formatSeparator||","}add(e,t){this.formats[e.toLowerCase().trim()]=t}addCached(e,t){this.formats[e.toLowerCase().trim()]=V(t)}format(e,t,s){let i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{};const o=t.split(this.formatSeparator);if(o.length>1&&o[0].indexOf("(")>1&&o[0].indexOf(")")<0&&o.find((e=>e.indexOf(")")>-1))){const e=o.findIndex((e=>e.indexOf(")")>-1));o[0]=[o[0],...o.splice(1,e)].join(this.formatSeparator)}return o.reduce(((e,t)=>{const{formatName:o,formatOptions:n}=(e=>{let t=e.toLowerCase().trim();const s={};if(e.indexOf("(")>-1){const i=e.split("(");t=i[0].toLowerCase().trim();const o=i[1].substring(0,i[1].length-1);"currency"===t&&o.indexOf(":")<0?s.currency||(s.currency=o.trim()):"relativetime"===t&&o.indexOf(":")<0?s.range||(s.range=o.trim()):o.split(";").forEach((e=>{if(e){const[t,...i]=e.split(":"),o=i.join(":").trim().replace(/^'+|'+$/g,""),n=t.trim();s[n]||(s[n]=o),"false"===o&&(s[n]=!1),"true"===o&&(s[n]=!0),isNaN(o)||(s[n]=parseInt(o,10))}}))}return{formatName:t,formatOptions:s}})(t);if(this.formats[o]){let t=e;try{const r=i&&i.formatParams&&i.formatParams[i.interpolationkey]||{},a=r.locale||r.lng||i.locale||i.lng||s;t=this.formats[o](e,a,{...n,...i,...r})}catch(e){this.logger.warn(e)}return t}return this.logger.warn(`there was no format function for ${o}`),e}),e)}}class U extends x{constructor(e,t,s){let i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{};super(),this.backend=e,this.store=t,this.services=s,this.languageUtils=s.languageUtils,this.options=i,this.logger=b.create("backendConnector"),this.waitingReads=[],this.maxParallelReads=i.maxParallelReads||10,this.readingCalls=0,this.maxRetries=i.maxRetries>=0?i.maxRetries:5,this.retryTimeout=i.retryTimeout>=1?i.retryTimeout:350,this.state={},this.queue=[],this.backend&&this.backend.init&&this.backend.init(s,i.backend,i)}queueLoad(e,t,s,i){const o={},n={},r={},a={};return e.forEach((e=>{let i=!0;t.forEach((t=>{const r=`${e}|${t}`;!s.reload&&this.store.hasResourceBundle(e,t)?this.state[r]=2:this.state[r]<0||(1===this.state[r]?void 0===n[r]&&(n[r]=!0):(this.state[r]=1,i=!1,void 0===n[r]&&(n[r]=!0),void 0===o[r]&&(o[r]=!0),void 0===a[t]&&(a[t]=!0)))})),i||(r[e]=!0)})),(Object.keys(o).length||Object.keys(n).length)&&this.queue.push({pending:n,pendingCount:Object.keys(n).length,loaded:{},errors:[],callback:i}),{toLoad:Object.keys(o),pending:Object.keys(n),toLoadLanguages:Object.keys(r),toLoadNamespaces:Object.keys(a)}}loaded(e,t,s){const i=e.split("|"),o=i[0],n=i[1];t&&this.emit("failedLoading",o,n,t),!t&&s&&this.store.addResourceBundle(o,n,s,void 0,void 0,{skipCopy:!0}),this.state[e]=t?-1:2,t&&s&&(this.state[e]=0);const a={};this.queue.forEach((s=>{((e,t,s,i)=>{const{obj:o,k:n}=r(e,t,Object);o[n]=o[n]||[],o[n].push(s)})(s.loaded,[o],n),((e,t)=>{void 0!==e.pending[t]&&(delete e.pending[t],e.pendingCount--)})(s,e),t&&s.errors.push(t),0!==s.pendingCount||s.done||(Object.keys(s.loaded).forEach((e=>{a[e]||(a[e]={});const t=s.loaded[e];t.length&&t.forEach((t=>{void 0===a[e][t]&&(a[e][t]=!0)}))})),s.done=!0,s.errors.length?s.callback(s.errors):s.callback())})),this.emit("loaded",a),this.queue=this.queue.filter((e=>!e.done))}read(e,t,s){let i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:0,o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:this.retryTimeout,n=arguments.length>5?arguments[5]:void 0;if(!e.length)return n(null,{});if(this.readingCalls>=this.maxParallelReads)return void this.waitingReads.push({lng:e,ns:t,fcName:s,tried:i,wait:o,callback:n});this.readingCalls++;const r=(r,a)=>{if(this.readingCalls--,this.waitingReads.length>0){const e=this.waitingReads.shift();this.read(e.lng,e.ns,e.fcName,e.tried,e.wait,e.callback)}r&&a&&i<this.maxRetries?setTimeout((()=>{this.read.call(this,e,t,s,i+1,2*o,n)}),o):n(r,a)},a=this.backend[s].bind(this.backend);if(2!==a.length)return a(e,t,r);try{const s=a(e,t);s&&"function"==typeof s.then?s.then((e=>r(null,e))).catch(r):r(null,s)}catch(e){r(e)}}prepareLoading(t,s){let i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},o=arguments.length>3?arguments[3]:void 0;if(!this.backend)return this.logger.warn("No backend was added via i18next.use. Will not load resources."),o&&o();e(t)&&(t=this.languageUtils.toResolveHierarchy(t)),e(s)&&(s=[s]);const n=this.queueLoad(t,s,i,o);if(!n.toLoad.length)return n.pending.length||o(),null;n.toLoad.forEach((e=>{this.loadOne(e)}))}load(e,t,s){this.prepareLoading(e,t,{},s)}reload(e,t,s){this.prepareLoading(e,t,{reload:!0},s)}loadOne(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";const s=e.split("|"),i=s[0],o=s[1];this.read(i,o,"read",void 0,void 0,((s,n)=>{s&&this.logger.warn(`${t}loading namespace ${o} for language ${i} failed`,s),!s&&n&&this.logger.log(`${t}loaded namespace ${o} for language ${i}`,n),this.loaded(e,s,n)}))}saveMissing(e,t,s,i,o){let n=arguments.length>5&&void 0!==arguments[5]?arguments[5]:{},r=arguments.length>6&&void 0!==arguments[6]?arguments[6]:()=>{};if(this.services.utils&&this.services.utils.hasLoadedNamespace&&!this.services.utils.hasLoadedNamespace(t))this.logger.warn(`did not save key "${s}" as the namespace "${t}" was not yet loaded`,"This means something IS WRONG in your setup. You access the t function before i18next.init / i18next.loadNamespace / i18next.changeLanguage was done. Wait for the callback or Promise to resolve before accessing it!!!");else if(null!=s&&""!==s){if(this.backend&&this.backend.create){const a={...n,isUpdate:o},l=this.backend.create.bind(this.backend);if(l.length<6)try{let o;o=5===l.length?l(e,t,s,i,a):l(e,t,s,i),o&&"function"==typeof o.then?o.then((e=>r(null,e))).catch(r):r(null,o)}catch(e){r(e)}else l(e,t,s,i,r,a)}e&&e[0]&&this.store.addResource(e[0],t,s,i)}}}const T=()=>({debug:!1,initImmediate:!0,ns:["translation"],defaultNS:["translation"],fallbackLng:["dev"],fallbackNS:!1,supportedLngs:!1,nonExplicitSupportedLngs:!1,load:"all",preload:!1,simplifyPluralSuffix:!0,keySeparator:".",nsSeparator:":",pluralSeparator:"_",contextSeparator:"_",partialBundledLanguages:!1,saveMissing:!1,updateMissing:!1,saveMissingTo:"fallback",saveMissingPlurals:!0,missingKeyHandler:!1,missingInterpolationHandler:!1,postProcess:!1,postProcessPassResolved:!1,returnNull:!1,returnEmptyString:!0,returnObjects:!1,joinArrays:!1,returnedObjectHandler:!1,parseMissingKeyHandler:!1,appendNamespaceToMissingKey:!1,appendNamespaceToCIMode:!1,overloadTranslationOptionHandler:t=>{let s={};if("object"==typeof t[1]&&(s=t[1]),e(t[1])&&(s.defaultValue=t[1]),e(t[2])&&(s.tDescription=t[2]),"object"==typeof t[2]||"object"==typeof t[3]){const e=t[3]||t[2];Object.keys(e).forEach((t=>{s[t]=e[t]}))}return s},interpolation:{escapeValue:!0,format:e=>e,prefix:"{{",suffix:"}}",formatSeparator:",",unescapePrefix:"-",nestingPrefix:"$t(",nestingSuffix:")",nestingOptionsSeparator:",",maxReplaces:1e3,skipOnVariables:!0}}),K=t=>(e(t.ns)&&(t.ns=[t.ns]),e(t.fallbackLng)&&(t.fallbackLng=[t.fallbackLng]),e(t.fallbackNS)&&(t.fallbackNS=[t.fallbackNS]),t.supportedLngs&&t.supportedLngs.indexOf("cimode")<0&&(t.supportedLngs=t.supportedLngs.concat(["cimode"])),t),M=()=>{};class z extends x{constructor(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments.length>1?arguments[1]:void 0;var s;if(super(),this.options=K(e),this.services={},this.logger=b,this.modules={external:[]},s=this,Object.getOwnPropertyNames(Object.getPrototypeOf(s)).forEach((e=>{"function"==typeof s[e]&&(s[e]=s[e].bind(s))})),t&&!this.isInitialized&&!e.isClone){if(!this.options.initImmediate)return this.init(e,t),this;setTimeout((()=>{this.init(e,t)}),0)}}init(){var s=this;let i=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},o=arguments.length>1?arguments[1]:void 0;this.isInitializing=!0,"function"==typeof i&&(o=i,i={}),!i.defaultNS&&!1!==i.defaultNS&&i.ns&&(e(i.ns)?i.defaultNS=i.ns:i.ns.indexOf("translation")<0&&(i.defaultNS=i.ns[0]));const n=T();this.options={...n,...this.options,...K(i)},"v1"!==this.options.compatibilityAPI&&(this.options.interpolation={...n.interpolation,...this.options.interpolation}),void 0!==i.keySeparator&&(this.options.userDefinedKeySeparator=i.keySeparator),void 0!==i.nsSeparator&&(this.options.userDefinedNsSeparator=i.nsSeparator);const r=e=>e?"function"==typeof e?new e:e:null;if(!this.options.isClone){let e;this.modules.logger?b.init(r(this.modules.logger),this.options):b.init(null,this.options),this.modules.formatter?e=this.modules.formatter:"undefined"!=typeof Intl&&(e=D);const t=new N(this.options);this.store=new k(this.options.resources,this.options);const i=this.services;i.logger=b,i.resourceStore=this.store,i.languageUtils=t,i.pluralResolver=new E(t,{prepend:this.options.pluralSeparator,compatibilityJSON:this.options.compatibilityJSON,simplifyPluralSuffix:this.options.simplifyPluralSuffix}),!e||this.options.interpolation.format&&this.options.interpolation.format!==n.interpolation.format||(i.formatter=r(e),i.formatter.init(i,this.options),this.options.interpolation.format=i.formatter.format.bind(i.formatter)),i.interpolator=new A(this.options),i.utils={hasLoadedNamespace:this.hasLoadedNamespace.bind(this)},i.backendConnector=new U(r(this.modules.backend),i.resourceStore,i,this.options),i.backendConnector.on("*",(function(e){for(var t=arguments.length,i=new Array(t>1?t-1:0),o=1;o<t;o++)i[o-1]=arguments[o];s.emit(e,...i)})),this.modules.languageDetector&&(i.languageDetector=r(this.modules.languageDetector),i.languageDetector.init&&i.languageDetector.init(i,this.options.detection,this.options)),this.modules.i18nFormat&&(i.i18nFormat=r(this.modules.i18nFormat),i.i18nFormat.init&&i.i18nFormat.init(this)),this.translator=new L(this.services,this.options),this.translator.on("*",(function(e){for(var t=arguments.length,i=new Array(t>1?t-1:0),o=1;o<t;o++)i[o-1]=arguments[o];s.emit(e,...i)})),this.modules.external.forEach((e=>{e.init&&e.init(this)}))}if(this.format=this.options.interpolation.format,o||(o=M),this.options.fallbackLng&&!this.services.languageDetector&&!this.options.lng){const e=this.services.languageUtils.getFallbackCodes(this.options.fallbackLng);e.length>0&&"dev"!==e[0]&&(this.options.lng=e[0])}this.services.languageDetector||this.options.lng||this.logger.warn("init: no languageDetector is used and no lng is defined");["getResource","hasResourceBundle","getResourceBundle","getDataByLanguage"].forEach((e=>{this[e]=function(){return s.store[e](...arguments)}}));["addResource","addResources","addResourceBundle","removeResourceBundle"].forEach((e=>{this[e]=function(){return s.store[e](...arguments),s}}));const a=t(),l=()=>{const e=(e,t)=>{this.isInitializing=!1,this.isInitialized&&!this.initializedStoreOnce&&this.logger.warn("init: i18next is already initialized. You should call init just once!"),this.isInitialized=!0,this.options.isClone||this.logger.log("initialized",this.options),this.emit("initialized",this.options),a.resolve(t),o(e,t)};if(this.languages&&"v1"!==this.options.compatibilityAPI&&!this.isInitialized)return e(null,this.t.bind(this));this.changeLanguage(this.options.lng,e)};return this.options.resources||!this.options.initImmediate?l():setTimeout(l,0),a}loadResources(t){let s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:M;const i=e(t)?t:this.language;if("function"==typeof t&&(s=t),!this.options.resources||this.options.partialBundledLanguages){if(i&&"cimode"===i.toLowerCase()&&(!this.options.preload||0===this.options.preload.length))return s();const e=[],t=t=>{if(!t)return;if("cimode"===t)return;this.services.languageUtils.toResolveHierarchy(t).forEach((t=>{"cimode"!==t&&e.indexOf(t)<0&&e.push(t)}))};if(i)t(i);else{this.services.languageUtils.getFallbackCodes(this.options.fallbackLng).forEach((e=>t(e)))}this.options.preload&&this.options.preload.forEach((e=>t(e))),this.services.backendConnector.load(e,this.options.ns,(e=>{e||this.resolvedLanguage||!this.language||this.setResolvedLanguage(this.language),s(e)}))}else s(null)}reloadResources(e,s,i){const o=t();return"function"==typeof e&&(i=e,e=void 0),"function"==typeof s&&(i=s,s=void 0),e||(e=this.languages),s||(s=this.options.ns),i||(i=M),this.services.backendConnector.reload(e,s,(e=>{o.resolve(),i(e)})),o}use(e){if(!e)throw new Error("You are passing an undefined module! Please check the object you are passing to i18next.use()");if(!e.type)throw new Error("You are passing a wrong module! Please check the object you are passing to i18next.use()");return"backend"===e.type&&(this.modules.backend=e),("logger"===e.type||e.log&&e.warn&&e.error)&&(this.modules.logger=e),"languageDetector"===e.type&&(this.modules.languageDetector=e),"i18nFormat"===e.type&&(this.modules.i18nFormat=e),"postProcessor"===e.type&&S.addPostProcessor(e),"formatter"===e.type&&(this.modules.formatter=e),"3rdParty"===e.type&&this.modules.external.push(e),this}setResolvedLanguage(e){if(e&&this.languages&&!(["cimode","dev"].indexOf(e)>-1))for(let e=0;e<this.languages.length;e++){const t=this.languages[e];if(!(["cimode","dev"].indexOf(t)>-1)&&this.store.hasLanguageSomeTranslations(t)){this.resolvedLanguage=t;break}}}changeLanguage(s,i){var o=this;this.isLanguageChangingTo=s;const n=t();this.emit("languageChanging",s);const r=e=>{this.language=e,this.languages=this.services.languageUtils.toResolveHierarchy(e),this.resolvedLanguage=void 0,this.setResolvedLanguage(e)},a=(e,t)=>{t?(r(t),this.translator.changeLanguage(t),this.isLanguageChangingTo=void 0,this.emit("languageChanged",t),this.logger.log("languageChanged",t)):this.isLanguageChangingTo=void 0,n.resolve((function(){return o.t(...arguments)})),i&&i(e,(function(){return o.t(...arguments)}))},l=t=>{s||t||!this.services.languageDetector||(t=[]);const i=e(t)?t:this.services.languageUtils.getBestMatchFromCodes(t);i&&(this.language||r(i),this.translator.language||this.translator.changeLanguage(i),this.services.languageDetector&&this.services.languageDetector.cacheUserLanguage&&this.services.languageDetector.cacheUserLanguage(i)),this.loadResources(i,(e=>{a(e,i)}))};return s||!this.services.languageDetector||this.services.languageDetector.async?!s&&this.services.languageDetector&&this.services.languageDetector.async?0===this.services.languageDetector.detect.length?this.services.languageDetector.detect().then(l):this.services.languageDetector.detect(l):l(s):l(this.services.languageDetector.detect()),n}getFixedT(t,s,i){var o=this;const n=function(e,t){let s;if("object"!=typeof t){for(var r=arguments.length,a=new Array(r>2?r-2:0),l=2;l<r;l++)a[l-2]=arguments[l];s=o.options.overloadTranslationOptionHandler([e,t].concat(a))}else s={...t};s.lng=s.lng||n.lng,s.lngs=s.lngs||n.lngs,s.ns=s.ns||n.ns,""!==s.keyPrefix&&(s.keyPrefix=s.keyPrefix||i||n.keyPrefix);const h=o.options.keySeparator||".";let u;return u=s.keyPrefix&&Array.isArray(e)?e.map((e=>`${s.keyPrefix}${h}${e}`)):s.keyPrefix?`${s.keyPrefix}${h}${e}`:e,o.t(u,s)};return e(t)?n.lng=t:n.lngs=t,n.ns=s,n.keyPrefix=i,n}t(){return this.translator&&this.translator.translate(...arguments)}exists(){return this.translator&&this.translator.exists(...arguments)}setDefaultNamespace(e){this.options.defaultNS=e}hasLoadedNamespace(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(!this.isInitialized)return this.logger.warn("hasLoadedNamespace: i18next was not initialized",this.languages),!1;if(!this.languages||!this.languages.length)return this.logger.warn("hasLoadedNamespace: i18n.languages were undefined or empty",this.languages),!1;const s=t.lng||this.resolvedLanguage||this.languages[0],i=!!this.options&&this.options.fallbackLng,o=this.languages[this.languages.length-1];if("cimode"===s.toLowerCase())return!0;const n=(e,t)=>{const s=this.services.backendConnector.state[`${e}|${t}`];return-1===s||0===s||2===s};if(t.precheck){const e=t.precheck(this,n);if(void 0!==e)return e}return!!this.hasResourceBundle(s,e)||(!(this.services.backendConnector.backend&&(!this.options.resources||this.options.partialBundledLanguages))||!(!n(s,e)||i&&!n(o,e)))}loadNamespaces(s,i){const o=t();return this.options.ns?(e(s)&&(s=[s]),s.forEach((e=>{this.options.ns.indexOf(e)<0&&this.options.ns.push(e)})),this.loadResources((e=>{o.resolve(),i&&i(e)})),o):(i&&i(),Promise.resolve())}loadLanguages(s,i){const o=t();e(s)&&(s=[s]);const n=this.options.preload||[],r=s.filter((e=>n.indexOf(e)<0&&this.services.languageUtils.isSupportedCode(e)));return r.length?(this.options.preload=n.concat(r),this.loadResources((e=>{o.resolve(),i&&i(e)})),o):(i&&i(),Promise.resolve())}dir(e){if(e||(e=this.resolvedLanguage||(this.languages&&this.languages.length>0?this.languages[0]:this.language)),!e)return"rtl";const t=this.services&&this.services.languageUtils||new N(T());return["ar","shu","sqr","ssh","xaa","yhd","yud","aao","abh","abv","acm","acq","acw","acx","acy","adf","ads","aeb","aec","afb","ajp","apc","apd","arb","arq","ars","ary","arz","auz","avl","ayh","ayl","ayn","ayp","bbz","pga","he","iw","ps","pbt","pbu","pst","prp","prd","ug","ur","ydd","yds","yih","ji","yi","hbo","men","xmn","fa","jpr","peo","pes","prs","dv","sam","ckb"].indexOf(t.getLanguagePartFromCode(e))>-1||e.toLowerCase().indexOf("-arab")>1?"rtl":"ltr"}static createInstance(){return new z(arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},arguments.length>1?arguments[1]:void 0)}cloneInstance(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:M;const s=e.forkResourceStore;s&&delete e.forkResourceStore;const i={...this.options,...e,isClone:!0},o=new z(i);void 0===e.debug&&void 0===e.prefix||(o.logger=o.logger.clone(e));return["store","services","language"].forEach((e=>{o[e]=this[e]})),o.services={...this.services},o.services.utils={hasLoadedNamespace:o.hasLoadedNamespace.bind(o)},s&&(o.store=new k(this.store.data,i),o.services.resourceStore=o.store),o.translator=new L(o.services,i),o.translator.on("*",(function(e){for(var t=arguments.length,s=new Array(t>1?t-1:0),i=1;i<t;i++)s[i-1]=arguments[i];o.emit(e,...s)})),o.init(i,t),o.translator.options=i,o.translator.backendConnector.services.utils={hasLoadedNamespace:o.hasLoadedNamespace.bind(o)},o}toJSON(){return{options:this.options,store:this.store,language:this.language,languages:this.languages,resolvedLanguage:this.resolvedLanguage}}}const H=z.createInstance();return H.createInstance=z.createInstance,H}));
